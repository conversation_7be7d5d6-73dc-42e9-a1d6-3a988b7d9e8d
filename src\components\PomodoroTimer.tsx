import React, { useState, useEffect, createContext, useContext } from "react";
import { Id } from "../../convex/_generated/dataModel";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Slider } from "@/components/ui/slider";
import { Play, Pause, Settings, RotateCcw, SkipForward } from "lucide-react";

// Pomodoro Settings Context
interface PomodoroSettings {
  workDuration: number; // in minutes
  shortBreakDuration: number; // in minutes
  longBreakDuration: number; // in minutes
  sessionsUntilLongBreak: number;
}

interface PomodoroContextType {
  settings: PomodoroSettings;
  updateSettings: (newSettings: Partial<PomodoroSettings>) => void;
}

const defaultSettings: PomodoroSettings = {
  workDuration: 25,
  shortBreakDuration: 5,
  longBreakDuration: 15,
  sessionsUntilLongBreak: 4,
};

const PomodoroContext = createContext<PomodoroContextType>({
  settings: defaultSettings,
  updateSettings: () => {},
});

const usePomodoroSettings = () => useContext(PomodoroContext);

// Settings Component
const PomodoroSettingsDialog: React.FC = () => {
  const { settings, updateSettings } = usePomodoroSettings();
  const [localSettings, setLocalSettings] = useState(settings);

  const handleSave = () => {
    updateSettings(localSettings);
  };

  const handleReset = () => {
    setLocalSettings(settings);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="text-white/70 hover:text-white"
        >
          <Settings className="h-5 w-5" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Pomodoro Settings</DialogTitle>
        </DialogHeader>
        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Work Duration: {localSettings.workDuration} minutes
            </label>
            <Slider
              value={[localSettings.workDuration]}
              onValueChange={(value) =>
                setLocalSettings({ ...localSettings, workDuration: value[0] })
              }
              max={60}
              min={5}
              step={5}
              className="w-full"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Short Break: {localSettings.shortBreakDuration} minutes
            </label>
            <Slider
              value={[localSettings.shortBreakDuration]}
              onValueChange={(value) =>
                setLocalSettings({
                  ...localSettings,
                  shortBreakDuration: value[0],
                })
              }
              max={15}
              min={1}
              step={1}
              className="w-full"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Long Break: {localSettings.longBreakDuration} minutes
            </label>
            <Slider
              value={[localSettings.longBreakDuration]}
              onValueChange={(value) =>
                setLocalSettings({
                  ...localSettings,
                  longBreakDuration: value[0],
                })
              }
              max={30}
              min={10}
              step={5}
              className="w-full"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Sessions until long break: {localSettings.sessionsUntilLongBreak}
            </label>
            <Slider
              value={[localSettings.sessionsUntilLongBreak]}
              onValueChange={(value) =>
                setLocalSettings({
                  ...localSettings,
                  sessionsUntilLongBreak: value[0],
                })
              }
              max={8}
              min={2}
              step={1}
              className="w-full"
            />
          </div>
          <div className="flex gap-2 pt-4">
            <Button onClick={handleSave} className="flex-1">
              Save Settings
            </Button>
            <Button onClick={handleReset} variant="outline" className="flex-1">
              Reset
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Segmented Circular Progress Component
const SegmentedCircularProgress: React.FC<{
  currentSession: number;
  totalSessions: number;
  currentProgress: number; // 0-100 for current session
  size: number;
  strokeWidth: number;
  className?: string;
}> = ({
  currentSession,
  totalSessions,
  currentProgress,
  size,
  strokeWidth,
  className = "",
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;

  // Calculate segments - each session gets equal space with small gaps
  const gapAngle = 10; // degrees for gaps between segments
  const totalGaps = totalSessions * gapAngle;
  const segmentAngle = (360 - totalGaps) / totalSessions;

  const segments = [];

  for (let i = 0; i < totalSessions; i++) {
    const startAngle = i * (segmentAngle + gapAngle);
    const segmentCircumference = (segmentAngle / 360) * circumference;

    let segmentProgress = 0;
    let segmentColor = "text-white/20"; // unfilled

    if (i < currentSession - 1) {
      // Completed sessions
      segmentProgress = 100;
      segmentColor = "text-orange-400";
    } else if (i === currentSession - 1) {
      // Current session
      segmentProgress = currentProgress;
      segmentColor = "text-orange-400";
    }

    const strokeDasharray = segmentCircumference;
    const strokeDashoffset =
      segmentCircumference - (segmentProgress / 100) * segmentCircumference;

    segments.push(
      <circle
        key={i}
        cx={size / 2}
        cy={size / 2}
        r={radius}
        stroke="currentColor"
        strokeWidth={strokeWidth}
        fill="transparent"
        strokeDasharray={`${strokeDasharray} ${circumference}`}
        strokeDashoffset={
          -startAngle * (circumference / 360) + strokeDashoffset
        }
        className={`${segmentColor} transition-all duration-300 ease-in-out`}
        strokeLinecap="round"
        style={{
          transformOrigin: `${size / 2}px ${size / 2}px`,
        }}
      />,
    );
  }

  return (
    <div className={`relative ${className}`}>
      <svg width={size} height={size} className="transform -rotate-90">
        {/* Background segments */}
        {Array.from({ length: totalSessions }).map((_, i) => {
          const startAngle = i * (segmentAngle + gapAngle);
          const segmentCircumference = (segmentAngle / 360) * circumference;

          return (
            <circle
              key={`bg-${i}`}
              cx={size / 2}
              cy={size / 2}
              r={radius}
              stroke="currentColor"
              strokeWidth={strokeWidth}
              fill="transparent"
              strokeDasharray={`${segmentCircumference} ${circumference}`}
              strokeDashoffset={-startAngle * (circumference / 360)}
              className="text-white/10"
              strokeLinecap="round"
            />
          );
        })}

        {/* Progress segments */}
        {segments}
      </svg>
    </div>
  );
};

// Main Timer Component
const PomodoroTimerComponent: React.FC<{ taskId: Id<"tasks"> }> = ({
  taskId,
}) => {
  const { settings } = usePomodoroSettings();
  const [timeRemaining, setTimeRemaining] = useState(
    settings.workDuration * 60,
  );
  const [isActive, setIsActive] = useState(false);
  const [currentSession, setCurrentSession] = useState(1);
  const [isBreak, setIsBreak] = useState(false);
  const [totalTimeSpent, setTotalTimeSpent] = useState(0);

  const updateTask = useMutation(api.tasks.updateTask);
  const getTask = useQuery(api.tasks.getTask, { taskId });

  // Calculate progress percentage
  const totalTime = isBreak
    ? (currentSession % settings.sessionsUntilLongBreak === 0
        ? settings.longBreakDuration
        : settings.shortBreakDuration) * 60
    : settings.workDuration * 60;
  const progress = ((totalTime - timeRemaining) / totalTime) * 100;

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (isActive && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining((prevTime) => {
          const newTime = prevTime - 1;
          if (!isBreak) {
            setTotalTimeSpent((prev) => prev + 1);
            // Update task time spent every 10 seconds to reduce API calls
            if ((totalTime - newTime) % 10 === 0) {
              const timeSpent = getTask?.timeSpent || 0;
              updateTask({ taskId, timeSpent: timeSpent + 10000 });
            }
          }
          return newTime;
        });
      }, 1000);
    } else if (timeRemaining === 0) {
      // Timer finished
      setIsActive(false);
      if (!isBreak) {
        // Work session finished, start break
        const isLongBreak =
          currentSession % settings.sessionsUntilLongBreak === 0;
        const breakDuration = isLongBreak
          ? settings.longBreakDuration
          : settings.shortBreakDuration;
        setTimeRemaining(breakDuration * 60);
        setIsBreak(true);
      } else {
        // Break finished, start next work session
        setTimeRemaining(settings.workDuration * 60);
        setIsBreak(false);
        setCurrentSession((prev) => {
          // Reset to 1 if we've completed all sessions in the cycle
          const nextSession = prev + 1;
          return nextSession > settings.sessionsUntilLongBreak
            ? 1
            : nextSession;
        });
      }
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [
    isActive,
    timeRemaining,
    isBreak,
    currentSession,
    settings,
    taskId,
    updateTask,
    getTask?.timeSpent,
    totalTime,
  ]);

  const toggleTimer = () => {
    setIsActive(!isActive);
  };

  const resetTimer = () => {
    setIsActive(false);
    setTimeRemaining(settings.workDuration * 60);
    setCurrentSession(1);
    setIsBreak(false);
    setTotalTimeSpent(0);
  };

  const skipSession = () => {
    setIsActive(false);
    if (!isBreak) {
      // Skip work session, go to break
      const isLongBreak =
        currentSession % settings.sessionsUntilLongBreak === 0;
      const breakDuration = isLongBreak
        ? settings.longBreakDuration
        : settings.shortBreakDuration;
      setTimeRemaining(breakDuration * 60);
      setIsBreak(true);
    } else {
      // Skip break, go to next work session
      setTimeRemaining(settings.workDuration * 60);
      setIsBreak(false);
      setCurrentSession((prev) => prev + 1);
    }
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}:${String(remainingSeconds).padStart(2, "0")}`;
    }
    return `${String(minutes).padStart(2, "0")}:${String(remainingSeconds).padStart(2, "0")}`;
  };

  const formatSessionTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}:${String(remainingSeconds).padStart(2, "0")}`;
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[500px] bg-slate-900 rounded-2xl p-8 relative">
      {/* Header */}
      <div className="absolute top-6 left-6 right-6 flex justify-between items-center">
        <div className="text-white/70 text-sm font-medium">
          {currentSession}
        </div>
        <PomodoroSettingsDialog />
      </div>

      {/* Mode indicator */}
      <div className="text-white/70 text-sm font-medium mb-8">
        {!isActive && timeRemaining < totalTime
          ? "paused"
          : isBreak
            ? "break mode"
            : "focus mode"}
      </div>

      {/* Circular timer */}
      <div className="relative mb-8">
        <SegmentedCircularProgress
          currentSession={currentSession}
          totalSessions={settings.sessionsUntilLongBreak}
          currentProgress={progress}
          size={280}
          strokeWidth={8}
          className="mb-4"
        />

        {/* Timer display */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className="text-white text-4xl font-light mb-2">
            {formatTime(timeRemaining)}
          </div>
          <div className="text-white/50 text-sm">
            {formatSessionTime(totalTimeSpent)}
          </div>
        </div>

        {/* Control buttons */}
        {isActive ? (
          /* Single pause button when active */
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
            <Button
              onClick={toggleTimer}
              size="icon"
              className="w-12 h-12 rounded-full bg-white text-slate-900 hover:bg-white/90"
            >
              <Pause className="h-6 w-6" />
            </Button>
          </div>
        ) : (
          /* Three buttons when paused */
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-4">
            <Button
              onClick={resetTimer}
              size="icon"
              className="w-12 h-12 rounded-full bg-white text-slate-900 hover:bg-white/90"
            >
              <RotateCcw className="h-5 w-5" />
            </Button>
            <Button
              onClick={toggleTimer}
              size="icon"
              className="w-14 h-14 rounded-full bg-white text-slate-900 hover:bg-white/90"
            >
              <Play className="h-7 w-7 ml-0.5" />
            </Button>
            <Button
              onClick={skipSession}
              size="icon"
              className="w-12 h-12 rounded-full bg-white text-slate-900 hover:bg-white/90"
            >
              <SkipForward className="h-5 w-5" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

// Provider Component
const PomodoroProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [settings, setSettings] = useState<PomodoroSettings>(() => {
    const saved = localStorage.getItem("pomodoro-settings");
    return saved ? JSON.parse(saved) : defaultSettings;
  });

  const updateSettings = (newSettings: Partial<PomodoroSettings>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    localStorage.setItem("pomodoro-settings", JSON.stringify(updatedSettings));
  };

  return (
    <PomodoroContext.Provider value={{ settings, updateSettings }}>
      {children}
    </PomodoroContext.Provider>
  );
};

// Main exported component
const PomodoroTimer: React.FC<{ taskId: Id<"tasks"> }> = ({ taskId }) => {
  return (
    <PomodoroProvider>
      <PomodoroTimerComponent taskId={taskId} />
    </PomodoroProvider>
  );
};

export default PomodoroTimer;
